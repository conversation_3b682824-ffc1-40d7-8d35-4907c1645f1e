"""
🧠 PATTERN IMPROVEMENT PROMPTS

This module provides LLM prompts specifically designed for iterative pattern improvement
rather than generating new patterns from scratch. This is the core of the Full Loop
Automation's iterative improvement architecture.

Key Principle: IMPROVE existing patterns, NOT create completely new ones each iteration.
"""

import config


class PatternImprovementPrompts:
    """
    Generates prompts for LLM pattern improvement based on performance feedback.
    
    This is fundamentally different from pattern discovery - it focuses on
    enhancing existing patterns based on specific performance metrics and failures.
    """
    
    @staticmethod
    def generate_improvement_prompt(current_patterns, performance_feedback, symbol=None):
        """
        Generate LLM prompt for improving existing patterns based on performance feedback.
        
        Args:
            current_patterns: The existing patterns that need improvement
            performance_feedback: Detailed performance analysis and failure reasons
            symbol: Trading symbol for context
            
        Returns:
            Formatted prompt for LLM pattern improvement
        """
        
        # Extract key performance metrics for prompt context
        overall_metrics = performance_feedback.get('overall_metrics', {})
        pattern_performance = performance_feedback.get('pattern_performance', [])
        improvement_suggestions = performance_feedback.get('improvement_suggestions', [])
        summary = performance_feedback.get('summary', 'No performance summary available')
        
        prompt = f"""
🧠 PATTERN IMPROVEMENT TASK - ITERATION {performance_feedback.get('iteration', 'N/A')}

You are an expert trading system optimizer. Your task is to IMPROVE the existing patterns below based on performance feedback, NOT create completely new patterns.

CRITICAL INSTRUCTION: You must enhance and optimize the existing patterns, not replace them with entirely different strategies.

📊 CURRENT PERFORMANCE ANALYSIS:
{summary}

📈 DETAILED PATTERN PERFORMANCE:
"""
        
        # Add detailed performance for each pattern
        for i, pattern_perf in enumerate(pattern_performance):
            prompt += f"""
Pattern {pattern_perf['pattern_number']}:
- Profitable: {'✅ YES' if pattern_perf['is_profitable'] else '❌ NO'}
- Sharpe Ratio: {pattern_perf['metrics'].get('sharpe_ratio', 0):.2f}
- Win Rate: {pattern_perf['metrics'].get('win_rate', 0):.1%}
- Profit Factor: {pattern_perf['metrics'].get('profit_factor', 0):.2f}
- Max Drawdown: {pattern_perf['metrics'].get('max_drawdown', 0):.1%}
- Trade Count: {pattern_perf['metrics'].get('trade_count', 0)}
- Total Return: {pattern_perf['metrics'].get('total_return', 0):.1f}%
"""
            
            # Add failure analysis if pattern failed
            if not pattern_perf['is_profitable'] and 'failure_reasons' in pattern_perf:
                prompt += f"- Failure Reasons: {', '.join(pattern_perf['failure_reasons'])}\n"
        
        prompt += f"""

🎯 SUCCESS CRITERIA TO ACHIEVE:
- Sharpe Ratio ≥ 1.5
- Win Rate ≥ 60%
- Profit Factor ≥ 1.3
- Max Drawdown ≤ 15%
- Trade Count ≥ 20

🔧 IMPROVEMENT SUGGESTIONS:
"""
        
        for suggestion in improvement_suggestions:
            prompt += f"- {suggestion}\n"
        
        prompt += f"""

📋 CURRENT PATTERNS TO IMPROVE:
{current_patterns}

🧠 YOUR IMPROVEMENT TASK:

Analyze the current patterns and their performance issues, then provide IMPROVED versions of these same patterns that address the specific problems identified.

IMPROVEMENT GUIDELINES:
1. Keep the core ORB (Opening Range Breakout) strategy approach
2. Adjust entry conditions to improve win rate and reduce false signals
3. Optimize stop loss and take profit levels based on performance feedback
4. Fine-tune timeframe and session filters if needed
5. Improve risk management to reduce drawdown
6. Maintain or increase trade frequency if it's too low

RESPONSE FORMAT:
Provide the improved patterns in the same format as the original patterns, with clear explanations of what you changed and why.

Focus on making targeted improvements rather than complete rewrites. The goal is evolution, not revolution.

Symbol Context: {symbol or 'Generic'}
Market Focus: Opening Range Breakout (ORB) patterns
Trading Style: Intraday breakout trading with proper risk management

Begin your improved patterns now:
"""
        
        return prompt.strip()
    
    @staticmethod
    def generate_context_reset_prompt(learning_summary, iteration_number):
        """
        Generate prompt for context reset while preserving key learning insights.
        
        Args:
            learning_summary: Condensed summary of key learning insights
            iteration_number: Current iteration number
            
        Returns:
            Context reset prompt with preserved learning
        """
        
        prompt = f"""
🔄 CONTEXT RESET - ITERATION {iteration_number}

You are continuing an iterative pattern improvement process. Previous context has been reset to manage memory, but key learning insights are preserved below.

📚 PRESERVED LEARNING INSIGHTS:
"""
        
        for insight in learning_summary:
            if insight.get('type') == 'top_strategy':
                prompt += f"""
Top Strategy #{insight.get('rank', '?')}:
- Sharpe Ratio: {insight.get('sharpe_ratio', 0):.2f}
- From Iteration: {insight.get('iteration', '?')}
- Guidance Used: {insight.get('guidance', 'N/A')}
"""
        
        prompt += f"""

🎯 CONTINUING MISSION:
Continue improving ORB (Opening Range Breakout) patterns to achieve:
- Sharpe Ratio ≥ 1.5
- Win Rate ≥ 60%
- Profit Factor ≥ 1.3
- Max Drawdown ≤ 15%
- Trade Count ≥ 20

Focus on iterative improvement of existing patterns rather than creating entirely new strategies.
"""
        
        return prompt.strip()
