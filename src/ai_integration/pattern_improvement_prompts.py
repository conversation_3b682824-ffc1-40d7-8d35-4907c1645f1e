"""
🧠 PATTERN IMPROVEMENT PROMPTS

This module provides LLM prompts specifically designed for iterative pattern improvement
rather than generating new patterns from scratch. This is the core of the Full Loop
Automation's iterative improvement architecture.

Key Principle: IMPROVE existing patterns, NOT create completely new ones each iteration.
"""

# import config  # Not used in this module


class PatternImprovementPrompts:
    """
    Generates prompts for LLM pattern improvement based on performance feedback.

    This is fundamentally different from pattern discovery - it focuses on
    enhancing existing patterns based on specific performance metrics and failures.
    """

    @staticmethod
    def get_schema_constraints():
        """Get the exact JSON schema constraints for pattern improvement"""
        return {
            "entry_conditions": [
                "orb_breakout_above", "orb_breakout_below", "opening_range_high", "opening_range_low",
                "close_above_orb_high", "close_below_orb_low", "candles_since_session_start", "session_filter",
                "orb_range_size", "orb_time_filter", "multi_timeframe_orb_confirm"
            ],
            "exit_conditions": [
                "trailing_stop_candle_low", "trailing_stop_candle_high", "fixed_pips_stop", "fixed_pips_target",
                "percentage_stop", "percentage_target", "orb_pattern_failure", "session_end_exit",
                "trailing_high_low", "support_resistance_exit", "time_based_exit", "profit_target_pips",
                "candle_close_opposite", "break_of_structure", "return_to_orb_range", "session_transition_exit",
                "trail_previous_candle_low", "trail_previous_candle_high", "orb_range_reentry", "new_session_exit"
            ],
            "entry_logic_options": ["AND", "OR"],
            "sessions": ["london", "ny", "asian", "london_ny_overlap", "all"],
            "timeframe_options": ["1min", "5min", "10min", "15min", "30min", "60min"]
        }
    
    @staticmethod
    def generate_improvement_prompt(current_patterns, performance_feedback, symbol=None):
        """
        Generate LLM prompt for improving existing patterns based on performance feedback.
        
        Args:
            current_patterns: The existing patterns that need improvement
            performance_feedback: Detailed performance analysis and failure reasons
            symbol: Trading symbol for context
            
        Returns:
            Formatted prompt for LLM pattern improvement
        """
        
        # Extract key performance metrics for prompt context
        pattern_performance = performance_feedback.get('pattern_performance', [])
        improvement_suggestions = performance_feedback.get('improvement_suggestions', [])
        summary = performance_feedback.get('summary', 'No performance summary available')
        
        prompt = f"""
🧠 PATTERN IMPROVEMENT TASK - ITERATION {performance_feedback.get('iteration', 'N/A')}

You are an expert trading system optimizer. Your task is to IMPROVE the existing patterns below based on performance feedback, NOT create completely new patterns.

CRITICAL INSTRUCTION: You must enhance and optimize the existing patterns, not replace them with entirely different strategies.

📊 CURRENT PERFORMANCE ANALYSIS:
{summary}

📈 DETAILED PATTERN PERFORMANCE:
"""
        
        # Add detailed performance for each pattern
        for i, pattern_perf in enumerate(pattern_performance):
            prompt += f"""
Pattern {pattern_perf['pattern_number']}:
- Profitable: {'✅ YES' if pattern_perf['is_profitable'] else '❌ NO'}
- Sharpe Ratio: {pattern_perf['metrics'].get('sharpe_ratio', 0):.2f}
- Win Rate: {pattern_perf['metrics'].get('win_rate', 0):.1%}
- Profit Factor: {pattern_perf['metrics'].get('profit_factor', 0):.2f}
- Max Drawdown: {pattern_perf['metrics'].get('max_drawdown', 0):.1%}
- Trade Count: {pattern_perf['metrics'].get('trade_count', 0)}
- Total Return: {pattern_perf['metrics'].get('total_return', 0):.1f}%
"""
            
            # Add failure analysis if pattern failed
            if not pattern_perf['is_profitable'] and 'failure_reasons' in pattern_perf:
                prompt += f"- Failure Reasons: {', '.join(pattern_perf['failure_reasons'])}\n"
        
        prompt += f"""

🎯 SUCCESS CRITERIA TO ACHIEVE:
- Sharpe Ratio ≥ 1.5
- Win Rate ≥ 60%
- Profit Factor ≥ 1.3
- Max Drawdown ≤ 15%
- Trade Count ≥ 20

🔧 IMPROVEMENT SUGGESTIONS:
"""
        
        for suggestion in improvement_suggestions:
            prompt += f"- {suggestion}\n"
        
        prompt += f"""

📋 CURRENT PATTERNS TO IMPROVE:
{current_patterns}

🧠 YOUR IMPROVEMENT TASK:

Analyze the current patterns and their performance issues, then provide IMPROVED versions of these same patterns that address the specific problems identified.

IMPROVEMENT GUIDELINES:
1. Keep the core ORB (Opening Range Breakout) strategy approach
2. Adjust entry conditions to improve win rate and reduce false signals
3. Optimize stop loss and take profit levels based on performance feedback
4. Fine-tune timeframe and session filters if needed
5. Improve risk management to reduce drawdown
6. Maintain or increase trade frequency if it's too low

        constraints = PatternImprovementPrompts.get_schema_constraints()

        prompt += "\\n\\nJSON SCHEMA CONSTRAINTS - MANDATORY FORMAT:\\n\\n"
        prompt += "**Entry Condition Types - Use ONLY these:**\\n"
        for condition in constraints['entry_conditions']:
            prompt += f"• {condition}\\n"

        prompt += "\\n**Exit Condition Types - Use ONLY these:**\\n"
        for condition in constraints['exit_conditions']:
            prompt += f"• {condition}\\n"

        prompt += f"\\n**Entry Logic Options:** {', '.join(constraints['entry_logic_options'])}\\n"
        prompt += f"**Sessions:** {', '.join(constraints['sessions'])}\\n"
        prompt += f"**Timeframe Options:** {', '.join(constraints['timeframe_options'])}\\n"

        json_example = '''

REQUIRED JSON OUTPUT FORMAT:

You MUST output valid JSON using the ORB-focused schema structure. For each improved pattern, provide a JSON object:

```json
{
  "pattern_name": "[Improved Pattern Name]",
  "description": "[Detailed description of improvements made]",
  "market_situation": "[Specific session/time conditions]",
  "entry_conditions": [
    {
      "condition": "orb_breakout_above",
      "orb_period_minutes": 30,
      "orb_period_bars": 3
    },
    {
      "condition": "session_filter",
      "value": "london"
    }
  ],
  "entry_logic": "AND",
  "exit_conditions": [
    {
      "condition": "fixed_pips_stop",
      "pips": 25
    },
    {
      "condition": "orb_pattern_failure",
      "trigger": "close_back_inside_range"
    }
  ],
  "position_sizing": {
    "method": "fixed_percent",
    "value": 0.02,
    "max_risk": 0.01
  },
  "optimal_conditions": {
    "timeframe": "15min",
    "session": "london"
  },
  "orb_logic": "[Explanation of why this ORB pattern works]"
}
```

'''
        prompt += json_example
        critical_rules = f'''CRITICAL RULES:
1. Use ONLY the condition types listed above
2. Do NOT use "orb_breakout_above_or_below" - use separate conditions instead
3. Do NOT include "MANDATORY_EXIT_EXAMPLES" or other non-schema fields
4. Output must be valid JSON array format: [pattern1, pattern2, ...]
5. Focus on making targeted improvements rather than complete rewrites

Symbol Context: {symbol or 'Generic'}
Market Focus: Opening Range Breakout (ORB) patterns
Trading Style: Intraday breakout trading with proper risk management

Begin your improved patterns now (JSON format only):'''

        prompt += critical_rules

        return prompt.strip()
    
    @staticmethod
    def generate_context_reset_prompt(learning_summary, iteration_number):
        """
        Generate prompt for context reset while preserving key learning insights.
        
        Args:
            learning_summary: Condensed summary of key learning insights
            iteration_number: Current iteration number
            
        Returns:
            Context reset prompt with preserved learning
        """
        
        prompt = f"""
🔄 CONTEXT RESET - ITERATION {iteration_number}

You are continuing an iterative pattern improvement process. Previous context has been reset to manage memory, but key learning insights are preserved below.

📚 PRESERVED LEARNING INSIGHTS:
"""
        
        for insight in learning_summary:
            if insight.get('type') == 'top_strategy':
                prompt += f"""
Top Strategy #{insight.get('rank', '?')}:
- Sharpe Ratio: {insight.get('sharpe_ratio', 0):.2f}
- From Iteration: {insight.get('iteration', '?')}
- Guidance Used: {insight.get('guidance', 'N/A')}
"""
        
        prompt += f"""

🎯 CONTINUING MISSION:
Continue improving ORB (Opening Range Breakout) patterns to achieve:
- Sharpe Ratio ≥ 1.5
- Win Rate ≥ 60%
- Profit Factor ≥ 1.3
- Max Drawdown ≤ 15%
- Trade Count ≥ 20

Focus on iterative improvement of existing patterns rather than creating entirely new strategies.
"""
        
        return prompt.strip()
